# Technomatix-AAS-ML Integration System

A Python system that integrates Technomatix Plant Simulation with Asset Administration Shell (AAS) servers and ML prediction services. Dynamically pulls metadata from AAS, extracts part dimensions from simulation, gets ML predictions, and updates processing times in real-time.

## Features

- Pure AAS-driven configuration - no hardcoded values
- Extracts part dimensions (Width, Thickness) from Technomatix simulation
- Gets ML service endpoint and parameters from AAS metadata
- Real-time ML predictions for processing time optimization
- Updates simulation processing times based on ML predictions
- Fail-fast architecture when AAS is unavailable

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run

```bash
python main.py
```

The system will:
1. Load configuration from AAS server
2. Connect to Technomatix and start simulation
3. Monitor for product arrivals at Station1
4. Extract part dimensions from simulation
5. Send data to ML service for predictions
6. Update processing times in simulation

## Project Structure

```
SIMULATION_TECHNMATIX/
├── main.py                      # Main orchestrator - run this
├── requirements.txt             # Python dependencies
├── html_start1.spp             # Technomatix simulation model
├── config/
│   └── settings.py             # System configuration
└── src/
    ├── aas/
    │   └── client.py           # AAS server communication
    ├── ml/
    │   ├── predictor.py        # ML service communication
    │   └── model_manager.py    # ML model configuration
    ├── simulation/
    │   ├── technomatix.py      # Technomatix integration
    │   └── monitor.py          # Simulation monitoring
    └── utils/
        └── logger.py           # Logging utilities
```

## Configuration

The system is designed for pure AAS-driven configuration. Key settings in `config/settings.py`:

| Setting                | Description                    | Default Value                                    |
| ---------------------- | ------------------------------ | ------------------------------------------------ |
| `MODEL_PATH`          | Path to Technomatix model     | `html_start1.spp`                              |
| `STATION_NAME`        | Station to monitor             | `Station1`                                      |
| `ML_REQUEST_TIMEOUT`  | ML service timeout (seconds)  | `10`                                            |
| `ML_RETRY_ATTEMPTS`   | ML service retry attempts      | `2`                                             |

All other configuration (ML endpoint, input/output parameters) comes from AAS server.

## How It Works

1. Loads ML service endpoint and parameters from AAS server
2. Connects to Technomatix Plant Simulation and starts simulation
3. Monitors Station1 for product arrivals
4. Extracts part dimensions (Width, Thickness) from simulation
5. Sends dimensions to ML service for processing time prediction
6. Updates simulation with predicted processing time
7. Continues monitoring until stopped

## Troubleshooting

**"Model file not found"**
- Check `MODEL_PATH` in `config/settings.py`
- Ensure the .spp file exists at the specified path

**"Failed to connect to Technomatix"**
- Ensure Technomatix Plant Simulation is installed
- Try running as Administrator

**"Cannot proceed without AAS metadata"**
- Check AAS server is running and accessible
- System requires AAS metadata to function - no fallback values
- Verify AAS server contains required metadata structure

**"ML service not available"**
- Check ML service is running at the endpoint specified in AAS
- System will continue monitoring but skip predictions if ML service is down

## Requirements

- Windows 10/11
- Python 3.8+
- Technomatix Plant Simulation
- AAS server with metadata structure
- ML prediction service

## Architecture

This system implements pure AAS-driven integration:

- Single command to run: `python main.py`
- Minimal configuration file: `config/settings.py`
- All metadata comes from AAS server
- Fail-fast when dependencies unavailable
- Clean modular architecture
- Professional logging output
