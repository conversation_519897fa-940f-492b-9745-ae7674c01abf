# ML Prediction Service

A simple REST API service for machine learning predictions. This service provides production time estimation based on input parameters (Width, Length, Height).

## 📁 Project Structure

```
Ml_Prediction/
├── main.py                    # Main entry point to start the service
├── requirements.txt           # Python dependencies
├── ERF-XGB_milling.pkl       # ML model file (pickle format)
├── src/                      # Source code directory
│   ├── __init__.py
│   ├── config.py             # Configuration settings
│   └── ml_predictor.py       # ML prediction logic
├── venv/                     # Virtual environment (created during setup)
└── README.md                 # This file
```

## 🚀 Quick Setup

### 1. Prerequisites
- Python 3.10 or higher
- Windows PowerShell (for Windows users)

### 2. Setup Virtual Environment
```powershell
# Create virtual environment
python -m venv venv

# Activate virtual environment
.\venv\Scripts\Activate.ps1

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration
Edit `src/config.py` to customize:
- **HOST**: Server IP address (default: `*********`)
- **PORT**: Server port (default: `9999`)
- **MODEL_PATH**: Path to the ML model file
- **HARDCODED_VALUES**: Model parameters

### 4. Start the Service
```powershell
# Make sure virtual environment is activated
python main.py
```

The service will start and display:
```
🚀 Starting ML Prediction Service v1.0.0
📡 Service will be available at: http://*********:9999
📋 API Documentation: http://*********:9999/docs
🔍 Health Check: http://*********:9999/health
```

## 🔗 API Endpoints

### Main Endpoints
- **GET** `/` - Service information and status
- **GET** `/health` - Health check
- **GET** `/model-info` - Model information
- **POST** `/predict` - Make predictions

### Interactive Documentation
Visit `http://*********:9999/docs` for interactive API documentation (Swagger UI).

## 📝 Making Predictions

### Request Format
```json
POST /predict
Content-Type: application/json

{
    "features": {
        "Width": 50.0,
        "Thickness": 100.0,
        "Height": 25.0
    }
}
```

### Response Format
```json
{
    "prediction": 12.34,
    "timestamp": "2025-07-15T14:30:00.123456",
    "status": "success"
}
```

### Example using curl
```bash
curl -X POST "http://*********:9999/predict" \
     -H "Content-Type: application/json" \
     -d '{"features": {"Width": 50.0, "Length": 100.0, "Height": 25.0}}'
```

### Example using PowerShell
```powershell
$body = @{
    features = @{
        Width = 50.0
        Length = 100.0
        Height = 25.0
    }
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://*********:9999/predict" -Method POST -Body $body -ContentType "application/json"
```

## 🛠️ Troubleshooting

### Common Issues

1. **Import Errors**
   ```
   Solution: Make sure virtual environment is activated and dependencies are installed
   pip install -r requirements.txt
   ```

2. **Model Loading Issues**
   ```
   The service includes a fallback model for testing if the original model fails to load
   Check the console output for model loading status
   ```

3. **Port Already in Use**
   ```
   Change the PORT in src/config.py to a different value (e.g., 8000, 8080)
   ```

4. **Network Access Issues**
   ```
   Make sure Windows Firewall allows the application
   Verify the IP address in src/config.py matches your network configuration
   ```

### Checking Service Status
```bash
# Health check
curl http://*********:9999/health

# Service info
curl http://*********:9999/
```

## 📋 Dependencies

- **FastAPI**: Web framework for building APIs
- **Uvicorn**: ASGI server for running FastAPI
- **Pydantic**: Data validation using Python type annotations
- **NumPy**: Numerical computing
- **Pandas**: Data manipulation and analysis
- **Scikit-learn**: Machine learning library
- **XGBoost**: Gradient boosting framework
- **Joblib**: Lightweight pipelining for Python

## 🔧 Customization

### Changing Server Configuration
Edit `src/config.py`:
```python
class Config:
    HOST = "your-ip-address"  # Change to your IP
    PORT = 8000               # Change to your preferred port
    # ... other settings
```

### Model Parameters
Adjust model parameters in `src/config.py`:
```python
HARDCODED_VALUES = {
    "Material": "Steel",
    "FeedRate": 500,
    "Tooth shape": "Square",
    "Number of teeth": 4
}
```

## 📞 Support

For issues or questions:
1. Check the console output for error messages
2. Verify all dependencies are installed correctly
3. Ensure the virtual environment is activated
4. Check network connectivity and firewall settings
