#!/usr/bin/env python3
"""AAS Basics - Real metadata fetch from AAS and ML endpoint discovery"""

# Allow running this file directly: add simulation_client to sys.path
import os, sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from services.AAS_communication.client import AASClient


def main():
    # 1) Where to contact AAS
    aas = AASClient()  # default URL inside AASClient
    aas_url = aas.server_url

    # 2) Get metadata from AAS
    metadata = aas.get_metadata_structure()

    # 3) Print simple fields
    print(f"AAS URL: {aas_url}")
    if not metadata:
        print("ML endpoint: None")
        print("Inputs: None")
        print("Output: None")
        return

    ml_endpoint = metadata.get('ml_endpoint')
    inputs = metadata.get('part_attributes')
    output = metadata.get('prediction_target')

    print(f"ML endpoint: {ml_endpoint}")
    print(f"Inputs: {inputs}")
    print(f"Output: {output}")

    aas.disconnect()


if __name__ == "__main__":
    main()
