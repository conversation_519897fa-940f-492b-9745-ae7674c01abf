#!/usr/bin/env python3
# Simple script: best-fit distribution for ProductionTime
import os, sys, math, numpy as np
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)
from data_analytics_application.services.data_analytics.data_fetch import get_record_ids, fetch_production_time

try:
    from fitter import Fitter
except Exception:
    print("fitter not installed. pip install fitter")
    raise

ids = get_record_ids()
vals = []
for rid in ids:
    v = fetch_production_time(rid)
    if v is not None and math.isfinite(v):
        vals.append(v)
if not vals:
    print("No production times found.")
else:
    data = np.array(vals, dtype=float)
    f = Fitter(data)
    f.fit()
    best = f.get_best()
    name = list(best.keys())[0]
    print(f"Best distribution: {name}")
    print(best[name])
    try:
        import matplotlib.pyplot as plt
        f.summary(); plt.show()
    except Exception:
        pass

