#!/usr/bin/env python3
"""
Test C: Technomatix Update
Tests if we can update processing time values in Technomatix
"""

import sys
import os
import logging

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from main.config import Config
from main.technomatix_monitor import TechnomatixMonitor

def main():
    print("=== Test C: Technomatix Update ===")
    print("Testing processing time updates in Technomatix...")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    config = Config()
    monitor = TechnomatixMonitor(config.MODEL_PATH)
    
    try:
        # Test 1: Connection
        print("\n1. Connecting to Technomatix...")
        if not monitor.connect():
            print(" Failed to connect to Technomatix")
            return 1
        print(" Connected successfully!")
        
        # Test 2: Read current processing time
        print("\n2. Reading current processing time...")
        station_info = monitor.get_station_info()
        current_time = station_info.get('processing_time', 0)
        print(f"   Current processing time: {current_time}")
        
        # Test 3: Update processing time
        print("\n3. Testing processing time update...")
        test_values = [25.0, 50.0, 75.0, current_time]  # Test different values, then restore
        
        for i, new_time in enumerate(test_values):
            print(f"   Test {i+1}: Updating to {new_time}...")
            
            if monitor.update_processing_time(new_time):
                # Verify the update
                station_info = monitor.get_station_info()
                actual_time = station_info.get('processing_time', 0)
                
                if abs(actual_time - new_time) < 0.01:  # Allow small floating point differences
                    print(f"    Successfully updated to {actual_time}")
                else:
                    print(f"    Update failed: expected {new_time}, got {actual_time}")
                    return 1
            else:
                print(f"    Failed to update processing time to {new_time}")
                return 1
        
        print("\n Test C completed successfully!")
        print(f"   Processing time restored to: {current_time}")
        return 0
        
    except Exception as e:
        print(f" Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        monitor.disconnect()
        print("Disconnected from Technomatix")

if __name__ == "__main__":
    sys.exit(main())
