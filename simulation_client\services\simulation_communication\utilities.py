#!/usr/bin/env python3
"""
Simulation Monitor Module
Monitors simulation for product arrivals and manages the ML prediction workflow
"""

import logging
import time
from typing import Optional, Dict, Any, List
from .client import TechnomatixConnector, StationStatus
from services.data_analytics_communication.client import M<PERSON><PERSON>ictor
from services.AAS_communication.client import <PERSON><PERSON><PERSON>
from configuration.settings import config


class SimulationMonitor:
    """Monitors simulation and coordinates ML prediction workflow"""
    
    def __init__(self, model_path: Optional[str] = None, aas_client: Optional[AASClient] = None):
        """Initialize simulation monitor"""
        self.logger = logging.getLogger(__name__)

        # Initialize components
        self.technomatix = TechnomatixConnector(model_path)
        self.ml_predictor = MLPredictor(aas_client=aas_client)
        self.aas_client = aas_client
        
        # Configuration
        self.station_name = config.STATION_NAME
        self.check_interval = config.PRODUCT_CHECK_INTERVAL
        
        # Tracking
        self.processed_products = 0
        self.last_product_id = None
        
    def connect(self) -> bool:
        """Connect to all required services"""
        # Connect to Technomatix
        if not self.technomatix.connect():
            self.logger.error("Failed to connect to Technomatix")
            return False
        
        # Test ML service connection
        if not self.ml_predictor.test_connection():
            self.logger.warning("ML service not available - predictions will be skipped")
        
        self.logger.info("Simulation monitor connected successfully")
        return True
    
    def disconnect(self):
        """Disconnect from all services"""
        self.technomatix.disconnect()
        self.logger.info("Simulation monitor disconnected")
    
    def start_simulation(self) -> bool:
        """Start the simulation"""
        return self.technomatix.start_simulation()
    
    def stop_simulation(self) -> bool:
        """Stop the simulation"""
        return self.technomatix.stop_simulation()
    
    def reset_simulation(self) -> bool:
        """Reset the simulation"""
        return self.technomatix.reset_simulation()
    
    def check_station_status(self, station_name: str = None) -> StationStatus:
        """Check if a product has arrived at the specified station"""
        station = station_name or self.station_name
        
        try:
            # Check station contents using hardcoded paths for now
            # In future, this will use metadata from AAS
            station_path = f".Models.Model.{station}"
            contents_attr = "numMU"
            
            num_products = self.technomatix.get_value(station_path, contents_attr)
            
            if num_products is None:
                return StationStatus.UNKNOWN
            
            if num_products > 0:
                self.logger.debug(f"Product detected at {station}: {num_products} products")
                return StationStatus.PRODUCT_ARRIVED
            else:
                return StationStatus.EMPTY
                
        except Exception as e:
            self.logger.error(f"Error checking {station} status: {e}")
            return StationStatus.UNKNOWN

    def get_station_info(self, station_name: str = None) -> Optional[Dict[str, Any]]:
        """Get station information including contents and attributes (igcv-i30 approach)"""
        station = station_name or self.station_name

        try:
            # Use configuration paths like igcv-i30
            station_info = {
                'name': station,
                'path': config.STATION1_PATH,
                'attributes': {}
            }

            # Get contentsList using GetValue
            try:
                contents_path = f"{config.STATION1_PATH}.{config.STATION1_CONTENTS_LIST_ATTR}"
                contents = self.technomatix.ps.GetValue(contents_path)

                # Handle different contentsList formats
                if contents and hasattr(contents, 'Count'):
                    # COM collection format
                    station_info['attributes']['contentsList_count'] = contents.Count
                    station_info['attributes']['contentsList_type'] = str(type(contents))
                    if contents.Count > 0:
                        try:
                            first_item = contents.Item(1)  # COM collections are 1-indexed
                            station_info['attributes']['contentsList_first_item'] = str(first_item)
                        except:
                            pass
                elif isinstance(contents, (tuple, list)) and len(contents) > 0:
                    # Tuple/list format (what we're seeing)
                    station_info['attributes']['contentsList_count'] = len(contents)
                    station_info['attributes']['contentsList_type'] = str(type(contents))
                    station_info['attributes']['contentsList_first_item'] = str(contents[0])
                    station_info['attributes']['contentsList_raw'] = str(contents)
                else:
                    station_info['attributes']['contentsList'] = str(contents)

            except Exception as e:
                station_info['attributes']['contentsList'] = f"Error: {e}"

            # Get numMU
            try:
                num_mu_path = f"{config.STATION1_PATH}.{config.STATION1_NUM_MU_ATTR}"
                num_mu = self.technomatix.ps.GetValue(num_mu_path)
                station_info['attributes']['numMU'] = num_mu
            except Exception as e:
                station_info['attributes']['numMU'] = f"Error: {e}"

            # Get processing time
            try:
                proc_time_path = f"{config.STATION1_PATH}.{config.STATION1_PROC_TIME_ATTR}"
                proc_time = self.technomatix.ps.GetValue(proc_time_path)
                station_info['attributes']['ProcTime'] = proc_time
            except Exception as e:
                station_info['attributes']['ProcTime'] = f"Error: {e}"

            return station_info

        except Exception as e:
            self.logger.error(f"Error getting station info for {station}: {e}")
            return None

    def extract_part_features(self, station_name: str = None) -> Optional[Dict[str, float]]:
        """Extract part features from simulation (Width from MUWidth, Thickness from MUHeight)"""
        station = station_name or self.station_name

        try:
            # Get required attributes from AAS metadata - fail if not available
            if not self.aas_client:
                self.logger.error("Cannot extract features without AAS client")
                return None

            metadata = self.aas_client.get_metadata_structure()
            if not metadata or not metadata.get('part_attributes'):
                self.logger.error("Cannot extract features without AAS metadata")
                return None

            required_attributes = metadata['part_attributes']

            features = {}

            # Check if station has parts
            station_info = self.get_station_info(station)
            if not station_info or station_info['attributes'].get('numMU', 0) == 0:
                self.logger.warning("No parts detected at station")
                return None

            # Build robust list of MU prefixes to avoid 'Void.MUWidth' errors on older models
            mu_prefixes: List[str] = []

            # 1) Try to locate the first MU from contentsList (works across model versions)
            try:
                contents_path = f"{config.STATION1_PATH}.{config.STATION1_CONTENTS_LIST_ATTR}"
                contents = self.technomatix.ps.GetValue(contents_path)
                first_mu = None
                if contents:
                    if hasattr(contents, 'Count') and getattr(contents, 'Count', 0) > 0:
                        try:
                            first_mu = contents.Item(1)
                        except Exception:
                            first_mu = None
                    elif isinstance(contents, (tuple, list)) and len(contents) > 0:
                        first_mu = contents[0]
                if first_mu:
                    mu_prefixes.append(str(first_mu))
            except Exception:
                pass

            # 2) Fallbacks used by different Plant Simulation versions
            mu_prefixes.extend([
                f"{config.STATION1_PATH}.cont.first",  # when cont is a container/list
                f"{config.STATION1_PATH}.cont",        # when cont points directly to MU
                f"{config.STATION1_PATH}.MU",          # when station exposes MU handle
            ])

            def _get_mu_attr(attr_name: str):
                for prefix in mu_prefixes:
                    # If prefix ends with '.cont', try both '.cont.first.attr' and '.cont.attr'
                    candidate_paths = []
                    if prefix.endswith('.cont'):
                        candidate_paths = [f"{prefix}.first.{attr_name}", f"{prefix}.{attr_name}"]
                    else:
                        candidate_paths = [f"{prefix}.{attr_name}"]

                    for full_path in candidate_paths:
                        try:
                            val = self.technomatix.ps.GetValue(full_path)
                            if val is not None:
                                self.logger.debug(f"Resolved {attr_name} via {full_path} -> {val}")
                                return val
                        except Exception:
                            continue
                return None

            # Extract Width (from MUWidth)
            if "Width" in required_attributes:
                width = _get_mu_attr("MUWidth")
                if width is not None:
                    features["Width"] = float(width)
                else:
                    self.logger.warning("Width could not be read from simulation; skipping feature")

            # Extract Thickness (mapped from MUHeight)
            if "Thickness" in required_attributes:
                thickness = _get_mu_attr("MUHeight")
                if thickness is not None:
                    features["Thickness"] = float(thickness)
                else:
                    self.logger.warning("Thickness (MUHeight) could not be read from simulation; skipping feature")



            # Note: Non-simulation parameters (Material, FeedRate, etc.) should be
            # defined in the AAS structure, not hardcoded here.
            # The AAS server should provide default values for these parameters.

            # Return extracted features
            if features:
                self.logger.info(f"SUCCESS: Extracted from simulation - Width={features.get('Width')}, Thickness={features.get('Thickness')}")
                return features
            else:
                self.logger.warning("WARNING: No features could be extracted")
                return None

        except Exception as e:
            self.logger.error(f"ERROR: Feature extraction failed - {e}")
            return None
    
    def update_processing_time(self, processing_time: float, station_name: str = None) -> bool:
        """Update processing time for a station"""
        station = station_name or self.station_name

        try:
            # Update Station1 processing time
            station_path = f"{config.STATION1_PATH}.{config.STATION1_PROC_TIME_ATTR}"

            # COM SetValue doesn't return success/failure, just call it
            self.technomatix.ps.SetValue(station_path, processing_time)

            # Verify the update by reading back the value
            updated_value = self.technomatix.ps.GetValue(station_path)
            if updated_value is not None and abs(float(updated_value) - processing_time) < 0.01:
                self.logger.info(f"SUCCESS: Updated Station1 processing time to {processing_time}")
                return True
            else:
                self.logger.error(f"ERROR: Processing time update verification failed - expected {processing_time}, got {updated_value}")
                return False

        except Exception as e:
            self.logger.error(f"ERROR: Failed to update processing time - {e}")
            return False

    def get_metadata_info(self) -> Dict[str, any]:
        """Get AAS metadata information for display"""
        try:
            if not self.aas_client:
                return {
                    'ml_endpoint': 'AAS client not available',
                    'inputs': [],
                    'output': 'AAS client not available',
                    'input_count': 0,
                    'output_count': 0
                }

            metadata = self.aas_client.get_metadata_structure()
            if not metadata:
                return {
                    'ml_endpoint': 'AAS metadata not available',
                    'inputs': [],
                    'output': 'AAS metadata not available',
                    'input_count': 0,
                    'output_count': 0
                }

            return {
                'ml_endpoint': metadata.get('ml_endpoint', 'Not specified in AAS'),
                'inputs': metadata.get('part_attributes', []),
                'output': metadata.get('prediction_target', 'Not specified in AAS'),
                'input_count': len(metadata.get('part_attributes', [])),
                'output_count': 1 if metadata.get('prediction_target') else 0
            }
        except Exception as e:
            self.logger.error(f"ERROR: Failed to get metadata info - {e}")
            return {
                'ml_endpoint': 'Error',
                'inputs': [],
                'output': 'Error',
                'input_count': 0,
                'output_count': 0
            }

    def get_ml_prediction(self, features: Dict[str, float]) -> Optional[float]:
        """Get ML prediction using the ML predictor"""
        try:
            prediction = self.ml_predictor.get_prediction(features)
            return prediction
        except Exception as e:
            self.logger.error(f"ERROR: ML prediction failed - {e}")
            return None

    def wait_for_product_arrival(self, check_interval: float = 1.0, timeout: float = 30.0) -> bool:
        """Wait for product arrival at station"""
        import time
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # Check if station has parts
                station_info = self.get_station_info(self.station_name)
                if station_info and station_info['attributes'].get('numMU', 0) > 0:
                    return True

                time.sleep(check_interval)

            except Exception as e:
                self.logger.debug(f"Error checking for product arrival: {e}")
                time.sleep(check_interval)

        return False
    
    def process_product_with_ml_prediction(self, station_name: str = None) -> bool:
        """Complete workflow: Extract features, get prediction, update processing time"""
        station = station_name or self.station_name
        
        try:
            # Step 1: Check if product is present
            status = self.check_station_status(station)
            if status != StationStatus.PRODUCT_ARRIVED:
                return False
            
            # Step 2: Extract part features
            features = self.extract_part_features(station)
            if not features:
                self.logger.error(f"Failed to extract features from {station}")
                return False
            
            # Step 3: Get ML prediction
            prediction = self.ml_predictor.get_prediction(features)
            if prediction is None:
                self.logger.warning(f"ML prediction failed for {station}, using default processing time")
                prediction = 10.0  # Default processing time

            # Step 4: Update processing time in Technomatix
            success = self.update_processing_time(station, prediction)

            if success:
                self.processed_products += 1
                self.logger.info(f"✅ Product #{self.processed_products} processed: {list(features.values())} → {prediction}")
                return True
            else:
                self.logger.error(f"Failed to update processing time for {station}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in ML prediction workflow for {station}: {e}")
            return False
    
    def monitor_simulation(self, duration: float) -> Dict[str, Any]:
        """Monitor simulation for specified duration"""
        start_time = time.time()
        initial_count = self.processed_products
        
        self.logger.info(f"Starting simulation monitoring for {duration} seconds")
        
        while (time.time() - start_time) < duration:
            try:
                # Process products with ML prediction
                if self.process_product_with_ml_prediction():
                    self.logger.debug(f"Product #{self.processed_products} processed")
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                self.logger.info("Monitoring interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Error during monitoring: {e}")
        
        products_processed = self.processed_products - initial_count
        elapsed_time = time.time() - start_time
        
        summary = {
            "duration": elapsed_time,
            "products_processed": products_processed,
            "total_products": self.processed_products,
            "processing_rate": products_processed / elapsed_time if elapsed_time > 0 else 0
        }
        
        self.logger.info(f"Monitoring completed: {summary}")
        return summary
    
    def get_status(self) -> Dict[str, Any]:
        """Get current monitor status"""
        metadata_info = None
        if self.aas_client:
            metadata_info = self.aas_client.get_metadata_structure()

        return {
            "connected": self.technomatix.is_connected,
            "station": self.station_name,
            "processed_products": self.processed_products,
            "ml_service_available": self.ml_predictor.test_connection(),
            "metadata_summary": metadata_info
        }
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
