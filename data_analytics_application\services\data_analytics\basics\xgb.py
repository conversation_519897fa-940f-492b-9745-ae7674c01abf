#!/usr/bin/env python3
# Simple script: train XGBoost and predict once, saving model next to this file
import os, sys, math
import pandas as pd
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)
from data_analytics_application.services.data_analytics.data_fetch import get_record_ids, fetch_production_time, fetch_factors
from data_analytics_application.services.data_analytics.train_xgb import build_dataset, build_pipeline, train_and_eval, save_model

MODEL_PATH = os.path.join(os.path.dirname(__file__), "ERF-XGB_milling.pkl")

# Gather data
ids = get_record_ids()
rows, times = [], []
for rid in ids:
    fac = fetch_factors(rid)
    t = fetch_production_time(rid)
    if fac is not None and t is not None and math.isfinite(t):
        rows.append(fac)
        times.append(float(t))
if not rows:
    print("No rows with both Factors and ProductionTime found.")
    raise SystemExit(0)

# Build df and train
cat_cols = ["ProductDescription", "Material", "ProductName"]
df = build_dataset(rows, times)
df["ProductDescription"] = pd.to_numeric(df.get("ProductDescription"), errors="coerce").fillna(0.0)
pipe = build_pipeline(cat_cols)
pipe, _ = train_and_eval(pipe, df, cat_cols)
save_model(pipe, MODEL_PATH)
print(f"Saved model to: {MODEL_PATH}")

# Single prediction
prod_desc = input("ProductDescription: ").strip()
material = input("Material: ").strip()
prod_name = input("ProductName: ").strip()
if not (prod_desc and material and prod_name):
    print("Missing input.")
else:
    sample = pd.DataFrame([{
        "ProductDescription": float(prod_desc) if prod_desc.replace('.', '', 1).isdigit() else prod_desc,
        "Material": material,
        "ProductName": prod_name,
    }])
    pred = float(pipe.predict(sample)[0])
    print(f"Predicted ProductionTime: {pred:.4f}")

