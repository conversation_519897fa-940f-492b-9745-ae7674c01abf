#!/usr/bin/env python3
"""
Station1 Monitoring Diagnostic Script
This script helps diagnose if Station1 monitoring is working correctly
"""

import sys
import os
import time
import logging

# Add the main directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'main'))

from technomatix_monitor import TechnomatixMonitor, StationStatus
from config import Config

def setup_logging():
    """Setup logging for diagnostics"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_station1_attributes(monitor):
    """Test reading various Station1 attributes"""
    print("\n=== Testing Station1 Attribute Access ===")
    
    if not monitor.is_connected:
        print(" Not connected to Technomatix")
        return False
    
    # Test different attribute paths
    test_attributes = [
        ("contentsList", f"{monitor.station1_path}.{monitor.station1_contents_list_attr}"),
        ("numMU", f"{monitor.station1_path}.{monitor.station1_num_mu_attr}"),
        ("procTime", f"{monitor.station1_path}.{monitor.station1_proc_time_attr}"),
        ("capacity", f"{monitor.station1_path}.capacity"),
        ("name", f"{monitor.station1_path}.name"),
        ("class", f"{monitor.station1_path}.class"),
    ]
    
    results = {}
    for attr_name, attr_path in test_attributes:
        try:
            value = monitor.ps.GetValue(attr_path)
            results[attr_name] = {
                "value": value,
                "type": type(value).__name__,
                "success": True
            }
            print(f" {attr_name}: {value} (type: {type(value).__name__})")
            
            # Special handling for contentsList
            if attr_name == "contentsList" and value:
                if hasattr(value, 'Count'):
                    print(f"   └─ Count: {value.Count}")
                    if value.Count > 0:
                        try:
                            first_item = value.Item(1)  # COM collections are 1-indexed
                            print(f"   └─ First item: {first_item}")
                        except Exception as e:
                            print(f"   └─ Could not access first item: {e}")
                elif isinstance(value, (list, tuple)):
                    print(f"   └─ Length: {len(value)}")
                    
        except Exception as e:
            results[attr_name] = {
                "value": None,
                "error": str(e),
                "success": False
            }
            print(f" {attr_name}: Error - {e}")
    
    return results

def test_station1_monitoring_loop(monitor, duration=10):
    """Test the monitoring loop for a short duration"""
    print(f"\n=== Testing Station1 Monitoring Loop ({duration}s) ===")
    print("Monitoring Station1 status every 1 second...")
    print("Status | contentsList | numMU | Details")
    print("-" * 50)
    
    start_time = time.time()
    check_count = 0
    status_changes = []
    
    while (time.time() - start_time) < duration:
        check_count += 1
        status = monitor.get_station1_status()
        
        # Get detailed info
        try:
            contents_path = f"{monitor.station1_path}.{monitor.station1_contents_list_attr}"
            contents = monitor.ps.GetValue(contents_path)
            contents_info = "None"
            if contents:
                if hasattr(contents, 'Count'):
                    contents_info = f"Count:{contents.Count}"
                elif isinstance(contents, (list, tuple)):
                    contents_info = f"Len:{len(contents)}"
                else:
                    contents_info = str(contents)[:20]
        except:
            contents_info = "Error"
            
        try:
            num_mu_path = f"{monitor.station1_path}.{monitor.station1_num_mu_attr}"
            num_mu = monitor.ps.GetValue(num_mu_path)
        except:
            num_mu = "Error"
        
        status_line = f"{status.value:12} | {contents_info:12} | {num_mu:5} | Check #{check_count}"
        print(status_line)
        
        # Track status changes
        if not status_changes or status_changes[-1] != status:
            status_changes.append(status)
        
        time.sleep(1)
    
    print(f"\n Monitoring Summary:")
    print(f"   Total checks: {check_count}")
    print(f"   Status changes: {len(status_changes)}")
    print(f"   Statuses seen: {[s.value for s in status_changes]}")
    
    return status_changes

def test_manual_part_detection(monitor):
    """Instructions for manual testing"""
    print("\n=== Manual Part Detection Test ===")
    print(" Instructions:")
    print("1. In Technomatix, manually drag a part to Station1")
    print("2. Watch the monitoring output below")
    print("3. Press Ctrl+C when done testing")
    print("\nMonitoring Station1 (press Ctrl+C to stop)...")
    
    try:
        check_count = 0
        while True:
            check_count += 1
            status = monitor.get_station1_status()
            
            # Get comprehensive info
            info = monitor.get_station_info()
            
            print(f"Check #{check_count:3d}: Status={status.value:15} | "
                  f"ProcTime={info.get('processing_time', 'N/A'):6} | "
                  f"Attrs={len(info.get('attributes', {})):2d}")
            
            if status == StationStatus.PRODUCT_ARRIVED:
                print(" PRODUCT DETECTED! Details:")
                for key, value in info.get('attributes', {}).items():
                    print(f"     {key}: {value}")
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print(f"\n Manual test stopped after {check_count} checks")

def main():
    """Main diagnostic function"""
    setup_logging()
    
    print("=== Station1 Monitoring Diagnostics ===")
    print("This script will help diagnose Station1 monitoring issues\n")
    
    # Initialize monitor
    monitor = TechnomatixMonitor()
    
    try:
        # Step 1: Connect
        print("1. Connecting to Technomatix...")
        if not monitor.connect():
            print(" Failed to connect to Technomatix")
            return
        print(" Connected to Technomatix")
        
        # Step 2: Test attribute access
        attr_results = test_station1_attributes(monitor)
        
        # Step 3: Test monitoring loop
        status_changes = test_station1_monitoring_loop(monitor, duration=10)
        
        # Step 4: Check if any products were detected
        if any(status == StationStatus.PRODUCT_ARRIVED for status in status_changes):
            print(" Product detection is working!")
        else:
            print("⚠ No products detected during monitoring")
            print("\nPossible issues:")
            print("- Source is not generating parts")
            print("- Parts are not reaching Station1")
            print("- Station1 path or attributes are incorrect")
            print("- Simulation needs manual start")
        
        # Step 5: Offer manual testing
        response = input("\nWould you like to test manual part detection? (y/n): ")
        if response.lower().startswith('y'):
            test_manual_part_detection(monitor)
        
    except KeyboardInterrupt:
        print("\n Diagnostics interrupted by user")
    except Exception as e:
        print(f" Error during diagnostics: {e}")
    finally:
        # Cleanup
        print("\n6. Cleaning up...")
        monitor.disconnect()
        print(" Disconnected from Technomatix")

if __name__ == "__main__":
    main()
