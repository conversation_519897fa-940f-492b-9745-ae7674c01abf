"""
Configuration settings for ML Prediction Service
"""

class Config:
    # Server configuration
    HOST = "*********"  # Change this to your server IP
    PORT = 9999          # Change this to your desired port

    # Model file path
    MODEL_PATH = "ERF-XGB_milling.pkl"

    # Service information
    SERVICE_NAME = "ML Prediction Service"
    SERVICE_VERSION = "1.0.0"

    # Model parameters (adjust as needed)
    # Note: Model uses Width, Thickness (mapped from Length), Tooth shape, Number of teeth
    HARDCODED_VALUES = {
        "Material": "Steel",      # For completeness
        "FeedRate": 500,          # For completeness
        "Tooth shape": "Square",  # Model parameter - adjust as needed
        "Number of teeth": 4      # Model parameter - adjust as needed
    }

    # API Endpoints
    ENDPOINTS = {
        "root": "/",
        "health": "/health",
        "predict": "/predict",
        "model_info": "/model-info"
    }

    # Request settings
    REQUEST_TIMEOUT = 30
    LOG_LEVEL = "info"
