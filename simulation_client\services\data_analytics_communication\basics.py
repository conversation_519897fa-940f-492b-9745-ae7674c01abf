#!/usr/bin/env python3
"""ML Basics - Simple: send values, print prediction"""

import json
import requests

PREDICT_URL = 'http://10.3.1.39:9999/predict'

# Minimal example features expected by the service
DEFAULT_FEATURES = {
    "Width": 95.2,
    "Thickness": 32.8
}

def predict():
    # 1) Where to send the request
    url = PREDICT_URL

    # 2) What we will send (the features for the model)
    features = DEFAULT_FEATURES
    payload = {"features": features}

    # 3) Show exactly what we send 
    print(f"URL: {url}")
    print(f"Features sent: {json.dumps(features)}")

    # 4) Make the HTTP request and get the response
    response = requests.post(url, json=payload, timeout=10, headers={"Content-Type": "application/json"})

    # 5) Read the prediction from the response
    try:
        response_json = response.json()
        prediction = response_json.get("prediction")
        print(f"Prediction: {prediction}")
    except Exception:
        print("Prediction: None")

if __name__ == '__main__':
    predict()
