#!/usr/bin/env python3
"""
CLI for data analytics tasks:
1) Compute mean of all ProductionTime values from AAS records
2) Fit best distribution using `fitter` and optionally plot
3) Train XGBoost model on Factors -> ProductionTime and enable interactive prediction

All endpoints/IPs are hardcoded to match user's environment.
"""

import sys
import math
from typing import List, Dict

import numpy as np
import pandas as pd

from data_fetch import get_record_ids, fetch_production_time, fetch_factors
from train_xgb import build_dataset, build_pipeline, train_and_eval, save_model

# Optional plotting / distribution fit
try:
    from fitter import Fitter
    HAS_FITTER = True
except Exception:
    HAS_FITTER = False

try:
    import matplotlib.pyplot as plt
    HAS_PLOT = True
except Exception:
    HAS_PLOT = False

import os
MODEL_OUTPUT_PATH = os.path.join(os.path.dirname(__file__), "ERF-XGB_milling.pkl")


def action_mean():
    ids = get_record_ids()
    print(f"Found {len(ids)} record IDs.")
    if ids:
        preview = ids[:10]
        print("Sample IDs:", ", ".join(preview) + (" ..." if len(ids) > len(preview) else ""))
    times: List[float] = []
    for rid in ids:
        t = fetch_production_time(rid)
        if t is not None and math.isfinite(t):
            times.append(t)
    if not times:
        print("No production times found.")
        return
    mean_val = float(np.mean(times))
    print(f"Records counted: {len(times)}")
    print(f"Mean ProductionTime: {mean_val:.4f}")


def action_distribution():
    ids = get_record_ids()
    times: List[float] = []
    for rid in ids:
        t = fetch_production_time(rid)
        if t is not None and math.isfinite(t):
            times.append(t)
    if not times:
        print("No production times found for distribution fitting.")
        return

    if not HAS_FITTER:
        print("fitter is not installed. Please run: pip install fitter")
        return

    data = np.array(times, dtype=float)
    f = Fitter(data)
    f.fit()
    best = f.get_best()
    # best is dict like { 'dist_name': {param: value} }
    dist_name = list(best.keys())[0]
    params = best[dist_name]
    print(f"Best distribution: {dist_name}")
    print(f"Parameters: {params}")

    if HAS_PLOT:
        try:
            f.summary()
            plt.show()
        except Exception:
            # If backend issue in headless env, skip
            pass
    else:
        print("matplotlib not installed; skipping plot.")


def action_list_records():
    ids = get_record_ids()
    print(f"TOTAL_RECORDS: {len(ids)}")
    for rid in ids:
        val = fetch_production_time(rid)
        print(f"{rid}\t{val}")


def action_xgboost():
    # 1) Collect dataset
    ids = get_record_ids()
    rows: List[Dict[str, str]] = []
    times: List[float] = []

    for rid in ids:
        fac = fetch_factors(rid)
        t = fetch_production_time(rid)
        if fac is not None and t is not None and math.isfinite(t):
            rows.append(fac)
            times.append(float(t))

    if not rows:
        print("No rows with both Factors and ProductionTime found.")
        return

    # Ensure required keys exist
    required = ["ProductDescription", "Material", "ProductName"]
    for r in rows:
        for k in required:
            if k not in r:
                print(f"Missing key {k} in a row; aborting.")
                return

    df = build_dataset(rows, times)

    # 2) Train
    categorical_cols = ["ProductDescription", "Material", "ProductName"]
    # Ensure ProductDescription is numeric; if all missing, fill zeros for stability
    try:
        df["ProductDescription"] = pd.to_numeric(df.get("ProductDescription"), errors="coerce")
    except Exception:
        df["ProductDescription"] = pd.NA
    if df["ProductDescription"].isna().all():
        df["ProductDescription"] = 0.0

    pipe = build_pipeline(categorical_cols)
    pipe, _ = train_and_eval(pipe, df, categorical_cols)

    # 3) Save model
    save_model(pipe, MODEL_OUTPUT_PATH)
    print(f"Saved model to: {MODEL_OUTPUT_PATH}")

    # 4) Interactive prediction
    print("\nEnter factors to get predicted ProductionTime.")
    try:
        while True:
            prod_desc = input("ProductDescription: ").strip()
            material = input("Material: ").strip()
            prod_name = input("ProductName: ").strip()
            if not (prod_desc and material and prod_name):
                print("Returning to menu.")
                break

            sample = pd.DataFrame([{
                "ProductDescription": prod_desc,
                "Material": material,
                "ProductName": prod_name,
            }])
            pred = float(pipe.predict(sample)[0])
            print(f"Predicted ProductionTime: {pred:.4f}")

            again = input("Press Enter to return to menu, or type 'again' to predict another: ").strip().lower()
            if again != "again":
                break
    except KeyboardInterrupt:
        print("\nExiting to menu.")


def main():
    print("Select an option:")
    print("1) Mean of ProductionTime")
    print("2) Best-fit distribution of ProductionTime")
    print("3) Train XGBoost on Factors and predict interactively")
    print("4) List all record names and ProductionTime values")
    choice = input("> ").strip()

    if choice == "1":
        action_mean()
    elif choice == "2":
        action_distribution()
    elif choice == "3":
        action_xgboost()
    elif choice == "4":
        action_list_records()
    else:
        print("Invalid option.")


if __name__ == "__main__":
    main()

