#!/usr/bin/env python3
"""
Configuration file for Technomatix-AAS Integration
Edit these values to customize the integration for your environment
"""

import os

class Config:
    """Configuration class for the Technomatix-AAS integration"""
    
    # ===== AAS SERVER CONFIGURATION =====
    AAS_HOST = "*********"
    AAS_PORT = 8081
    
    # Full AAS endpoint URL for production time
    AAS_PRODUCTION_TIME_URL = f"http://{AAS_HOST}:{AAS_PORT}/submodels/aHR0cHM6Ly9leGFtcGxlLmNvbS9pZHMvc20vNTM5NF8xMTUyXzMwNDJfODQ4OQ/submodel-elements/ProductionOperationRecords.ProductionTime/$value"
    
    # AAS request timeout in seconds
    AAS_TIMEOUT = 5
    
    # ===== TECHNOMATIX CONFIGURATION =====
    # Path to the Technomatix model file (.spp)
    MODEL_PATH = r"C:\Users\<USER>\Documents\SIMULATION_TECHNMATIX\html_start1.spp"
    
    # Technomatix object paths
    STATION1_PATH = ".Models.Model.Station1"
    EVENT_CONTROLLER_PATH = ".Models.Model.EventController"
    
    # Station1 attributes
    STATION1_PROC_TIME_ATTR = "ProcTime"
    STATION1_CONTENTS_LIST_ATTR = "contentsList"
    STATION1_NUM_MU_ATTR = "numMU"
    
    # ===== SIMULATION CONFIGURATION =====
    # How long to run the simulation monitoring (in seconds)
    SIMULATION_MONITOR_TIME = 100  # 5 minutes
    
    # How often to check for new products (in seconds)
    PRODUCT_CHECK_INTERVAL = 1
    
    # ===== LOGGING CONFIGURATION =====
    LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
    LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
    
    # ===== BACKUP CONFIGURATION =====
    # Backup directory for .spp files
    BACKUP_DIR = os.path.join(os.path.dirname(MODEL_PATH), "backups")
    
    @classmethod
    def validate(cls):
        """Validate configuration settings"""
        errors = []
        
        # Check if model file exists
        if not os.path.exists(cls.MODEL_PATH):
            errors.append(f"Model file not found: {cls.MODEL_PATH}")
        
        # Check AAS URL format
        if not cls.AAS_PRODUCTION_TIME_URL.startswith("http"):
            errors.append("AAS_PRODUCTION_TIME_URL must start with http:// or https://")
        
        # Check timeout values
        if cls.AAS_TIMEOUT <= 0:
            errors.append("AAS_TIMEOUT must be positive")
        
        if cls.PRODUCT_CHECK_INTERVAL <= 0:
            errors.append("PRODUCT_CHECK_INTERVAL must be positive")
        
        return errors
    
    @classmethod
    def print_config(cls):
        """Print current configuration"""
        print("=== Current Configuration ===")
        print(f"AAS Server: {cls.AAS_HOST}:{cls.AAS_PORT}")
        print(f"AAS URL: {cls.AAS_PRODUCTION_TIME_URL}")
        print(f"Model Path: {cls.MODEL_PATH}")
        print(f"Monitor Time: {cls.SIMULATION_MONITOR_TIME}s")
        print(f"Check Interval: {cls.PRODUCT_CHECK_INTERVAL}s")
        print(f"Log Level: {cls.LOG_LEVEL}")
        print("=" * 30)

# Example of how to override configuration
class DevelopmentConfig(Config):
    """Development configuration with different settings"""
    SIMULATION_MONITOR_TIME = 60  # Shorter monitoring for testing
    LOG_LEVEL = "DEBUG"

class ProductionConfig(Config):
    """Production configuration"""
    SIMULATION_MONITOR_TIME = 3600  # 1 hour monitoring
    LOG_LEVEL = "INFO"
