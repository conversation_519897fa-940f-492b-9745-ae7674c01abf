#!/usr/bin/env python3
"""
Simple Station1 Check Script
Quick diagnostic to see what's happening with Station1
"""

import sys
import os
import time
import win32com.client

# Add the main directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'main'))

from config import Config

def simple_station1_check():
    """Simple check of Station1 status"""
    config = Config()
    
    print("=== Simple Station1 Check ===")
    print(f"Model: {config.MODEL_PATH}")
    print(f"Station1 Path: {config.STATION1_PATH}")
    print()
    
    try:
        # Connect to Technomatix
        print("1. Connecting to Technomatix...")
        ps = win32com.client.Dispatch("Tecnomatix.PlantSimulation.RemoteControl")
        ps.SetTrustModels(True)
        ps.LoadModel(config.MODEL_PATH)
        ps.SetVisible(True)
        print(" Connected")
        
        # Start simulation
        print("\n2. Starting simulation...")
        try:
            ps.ExecuteSimTalk(f"{config.EVENT_CONTROLLER_PATH}.start")
            print(" Simulation started")
        except Exception as e:
            print(f"⚠ Could not start simulation: {e}")
        
        # Check Station1 attributes
        print("\n3. Checking Station1 attributes...")
        
        attributes_to_check = [
            ("contentsList", config.STATION1_CONTENTS_LIST_ATTR),
            ("numMU", config.STATION1_NUM_MU_ATTR),
            ("procTime", config.STATION1_PROC_TIME_ATTR),
            ("capacity", "capacity"),
            ("name", "name")
        ]
        
        for attr_display, attr_name in attributes_to_check:
            try:
                attr_path = f"{config.STATION1_PATH}.{attr_name}"
                value = ps.GetValue(attr_path)
                
                if attr_name == config.STATION1_CONTENTS_LIST_ATTR and value:
                    if hasattr(value, 'Count'):
                        print(f"   {attr_display}: Count = {value.Count}")
                        if value.Count > 0:
                            print(f"      └─ Has {value.Count} items!")
                    elif isinstance(value, (list, tuple)):
                        print(f"   {attr_display}: List with {len(value)} items")
                    else:
                        print(f"   {attr_display}: {value}")
                else:
                    print(f"   {attr_display}: {value}")
                    
            except Exception as e:
                print(f"   {attr_display}:  Error - {e}")
        
        # Monitor for 15 seconds
        print(f"\n4. Monitoring Station1 for 15 seconds...")
        print("Time | contentsList | numMU | Status")
        print("-" * 40)
        
        for i in range(15):
            try:
                # Check contentsList
                contents_path = f"{config.STATION1_PATH}.{config.STATION1_CONTENTS_LIST_ATTR}"
                contents = ps.GetValue(contents_path)
                
                if contents and hasattr(contents, 'Count'):
                    contents_str = f"Count:{contents.Count}"
                    has_parts = contents.Count > 0
                elif isinstance(contents, (list, tuple)):
                    contents_str = f"Len:{len(contents)}"
                    has_parts = len(contents) > 0
                else:
                    contents_str = str(contents)[:10] if contents else "None"
                    has_parts = False
                
                # Check numMU
                try:
                    num_mu_path = f"{config.STATION1_PATH}.{config.STATION1_NUM_MU_ATTR}"
                    num_mu = ps.GetValue(num_mu_path)
                    num_mu_str = str(num_mu)
                except:
                    num_mu_str = "Error"
                    num_mu = 0
                
                # Determine status
                if has_parts or (isinstance(num_mu, (int, float)) and num_mu > 0):
                    status = "🟢 PRODUCT!"
                else:
                    status = " Empty"
                
                print(f"{i+1:4d} | {contents_str:12} | {num_mu_str:5} | {status}")
                
                if has_parts:
                    print(f"      PRODUCT DETECTED at second {i+1}!")
                
            except Exception as e:
                print(f"{i+1:4d} | Error: {e}")
            
            time.sleep(1)
        
        print("\n5. Final check - Station1 comprehensive info...")
        try:
            # Get all available attributes
            station_path = config.STATION1_PATH
            
            # Try to get object info
            try:
                station_name = ps.GetValue(f"{station_path}.name")
                print(f"   Station name: {station_name}")
            except:
                pass
                
            try:
                station_class = ps.GetValue(f"{station_path}.class")
                print(f"   Station class: {station_class}")
            except:
                pass
            
            # Check if there are any parts in the system
            try:
                # Check Source
                source_path = ".Models.Model.Source"
                source_created = ps.GetValue(f"{source_path}.created")
                print(f"   Source created parts: {source_created}")
            except Exception as e:
                print(f"   Source info: Error - {e}")
                
        except Exception as e:
            print(f"   Final check error: {e}")
        
        # Stop simulation
        print("\n6. Stopping simulation...")
        try:
            ps.ExecuteSimTalk(f"{config.EVENT_CONTROLLER_PATH}.stop")
            print(" Simulation stopped")
        except Exception as e:
            print(f"⚠ Could not stop simulation: {e}")
            
    except Exception as e:
        print(f" Error: {e}")
    
    print("\n=== Check Complete ===")

if __name__ == "__main__":
    simple_station1_check()
