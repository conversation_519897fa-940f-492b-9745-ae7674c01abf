#!/usr/bin/env python3
"""
Training helpers for XGBoost regression using Factors -> ProductionTime.
Features: ProductDescription, Material, ProductName (string categorical)
Target: ProductionTime (float seconds)
"""

from typing import List, Dict, Tuple
import pandas as pd
import numpy as np
from sklearn.preprocessing import OneHotEncoder, StandardScaler
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_absolute_error
from xgboost import XGBRegressor
import joblib


def build_dataset(rows: List[Dict[str, str]], times: List[float]) -> pd.DataFrame:
    """Construct DataFrame from factor dicts and production times."""
    df_factors = pd.DataFrame(rows)
    df_factors["ProductionTime"] = np.array(times, dtype=float)
    return df_factors


def build_pipeline(categorical_cols: List[str]) -> Pipeline:
    """Pipeline: impute+scale numeric (ProductDescription) + OneHot categorical -> XGBRegressor.
    Avoid local lambdas/inner functions to keep the pipeline picklable.
    """

    numeric_cols = ["ProductDescription"]
    cat_cols = [c for c in categorical_cols if c != "ProductDescription"]

    preproc = ColumnTransformer(
        transformers=[
            ("num", Pipeline(steps=[
                ("impute", SimpleImputer(strategy="constant", fill_value=0.0, keep_empty_features=True)),
                ("scale", StandardScaler(with_mean=False)),
            ]), numeric_cols),
            ("cat", OneHotEncoder(handle_unknown="ignore"), cat_cols),
        ],
        remainder="drop",
    )

    model = XGBRegressor(
        n_estimators=400,
        max_depth=4,
        learning_rate=0.05,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=1.0,
        random_state=42,
        n_jobs=4,
        objective="reg:squarederror",
    )

    pipe = Pipeline(steps=[("preprocess", preproc), ("model", model)])
    return pipe


def train_and_eval(pipeline: Pipeline, df: pd.DataFrame, categorical_cols: List[str]) -> Tuple[Pipeline, float]:
    """
    Train on all data (small dataset). Report MAE via simple holdout if possible.
    If data < 6, train on all and compute in-sample MAE.
    """
    X = df[categorical_cols]
    y = df["ProductionTime"].astype(float)

    if len(df) >= 6:
        # Simple split
        idx = int(0.8 * len(df))
        X_train, X_test = X.iloc[:idx], X.iloc[idx:]
        y_train, y_test = y.iloc[:idx], y.iloc[idx:]
        pipeline.fit(X_train, y_train)
        y_pred = pipeline.predict(X_test)
        mae = float(mean_absolute_error(y_test, y_pred))
    else:
        pipeline.fit(X, y)
        y_pred = pipeline.predict(X)
        mae = float(mean_absolute_error(y, y_pred))

    return pipeline, mae


def save_model(pipeline: Pipeline, path: str) -> None:
    joblib.dump(pipeline, path)

