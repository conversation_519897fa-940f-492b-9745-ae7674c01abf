#!/usr/bin/env python3
"""Simulation Basics - Real connect to Technomatix and read/write ProcTime"""

# Allow running this file directly: add simulation_client to sys.path
import os, sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from configuration.settings import config
from services.simulation_communication.client import TechnomatixConnector

import time

# Simple demo parameters (edit these two numbers only)
PROC_TIME_TO_SET = 18.0      # minutes to write to Station1.ProcTime
MONITOR_ALIVE_SECONDS = 60.0 # seconds to keep reading MU parameters


def _is_mu_stable(sim: TechnomatixConnector, stability_checks: int = 3):
    """Check if MU count is stable (same value across multiple quick checks)."""
    try:
        counts = []
        for _ in range(stability_checks):
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
            counts.append(int(num_mu) if num_mu else 0)
            time.sleep(0.02)  # Very short delay

        # All counts should be the same and > 0
        return len(set(counts)) == 1 and counts[0] > 0
    except Exception:
        return False


def _resolve_mu_value(sim: TechnomatixConnector, attr_name: str, debug: bool = False):
    """Robustly resolve MU attribute across different model versions.
    Returns the value or None.
    """
    prefixes = []

    # Try to get first MU from contentsList
    try:
        contents_path = f"{config.STATION1_PATH}.{config.STATION1_CONTENTS_LIST_ATTR}"
        contents = sim.ps.GetValue(contents_path)
        first_mu = None
        if contents:
            if hasattr(contents, 'Count') and getattr(contents, 'Count', 0) > 0:
                try:
                    first_mu = contents.Item(1)
                except Exception:
                    first_mu = None
            elif isinstance(contents, (tuple, list)) and len(contents) > 0:
                first_mu = contents[0]
        if first_mu:
            prefixes.append(str(first_mu))
            if debug:
                print(f"  DEBUG: Found MU from contentsList: {first_mu}")
    except Exception as e:
        if debug:
            print(f"  DEBUG: contentsList failed: {e}")

    # Fallbacks: prefer .MU over .cont because cont can be Void intermittently
    prefixes.extend([
        f"{config.STATION1_PATH}.MU",
        f"{config.STATION1_PATH}.cont",
    ])

    # Try each prefix
    for p in prefixes:
        try:
            if debug:
                print(f"  DEBUG: Trying {p}.{attr_name}")
            val = sim.ps.GetValue(f"{p}.{attr_name}")
            if val is not None:
                if debug:
                    print(f"  DEBUG: SUCCESS: {p}.{attr_name} = {val}")
                return val
            elif debug:
                print(f"  DEBUG: {p}.{attr_name} returned None")
        except Exception as e:
            if debug:
                print(f"  DEBUG: {p}.{attr_name} failed: {e}")

    if debug:
        print(f"  DEBUG: All attempts failed for {attr_name}")
    return None


def _get_mu_attr_stable(sim: TechnomatixConnector, attr_name: str, timeout: float = 0.8, poll_interval: float = 0.05):
    """Read MU attribute robustly during handover timing.

    If numMU <= 0 -> return None immediately.
    Otherwise, retry until timeout for attr to be non-None.
    """
    end = time.time() + timeout
    while time.time() < end:
        try:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
            if not num_mu or int(num_mu) <= 0:
                return None
        except Exception:
            return None
        val = _resolve_mu_value(sim, attr_name)
        if val is not None:
            return val
        time.sleep(poll_interval)
    return None


def _get_mu_dims_stable(sim: TechnomatixConnector, debug: bool = False):
    """Get MU dimensions using stable COM-based method.
    Assumes caller has already verified MU stability.
    """
    if debug:
        print(f"  DEBUG: Getting MU dimensions...")

    try:
        w = _resolve_mu_value(sim, "MUWidth", debug=debug)
        h = _resolve_mu_value(sim, "MUHeight", debug=debug)
        if debug:
            print(f"  DEBUG: Got dimensions: w={w}, h={h}")
        return w, h
    except Exception as e:
        if debug:
            print(f"  DEBUG: Exception in _get_mu_dims_stable: {e}")
        return None, None





def main():

    with TechnomatixConnector(model_path=config.MODEL_PATH) as sim:

        station_path = config.STATION1_PATH
        proc_attr = config.STATION1_PROC_TIME_ATTR

        # Read and write ProcTime
        sim.get_value(station_path, proc_attr)
        sim.set_value(station_path, proc_attr, PROC_TIME_TO_SET)

        # Keep reading MU parameters for MONITOR_ALIVE_SECONDS
        end_time = time.time() + MONITOR_ALIVE_SECONDS
        while time.time() < end_time:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)

            # Only try to get MU dimensions if MU state is stable
            if _is_mu_stable(sim):
                try:
                    w, h = _get_mu_dims_stable(sim, debug=False)  # Turn off debug for cleaner output
                except Exception as e:
                    print(f"  DEBUG: Exception during dimension read: {e}")
                    w, h = None, None
            else:
                w, h = None, None

            print(f"numMU: {num_mu} | MUWidth: {w} | MUHeight: {h}")
            time.sleep(1.0)



if __name__ == "__main__":
    main()

