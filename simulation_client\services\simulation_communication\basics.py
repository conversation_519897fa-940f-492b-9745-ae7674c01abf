#!/usr/bin/env python3
"""Simulation Basics - Real connect to Technomatix and read/write ProcTime"""

# Allow running this file directly: add simulation_client to sys.path
import os, sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from configuration.settings import config
from services.simulation_communication.client import TechnomatixConnector

import time

# Simple demo parameters (edit these two numbers only)
PROC_TIME_TO_SET = 18.0      # minutes to write to Station1.ProcTime
MONITOR_ALIVE_SECONDS = 60.0 # seconds to keep reading MU parameters


def main():

    with TechnomatixConnector(model_path=config.MODEL_PATH) as sim:

        station_path = config.STATION1_PATH
        proc_attr = config.STATION1_PROC_TIME_ATTR
         # 
        # Read and write ProcTime
        sim.get_value(station_path, proc_attr)
        sim.set_value(station_path, proc_attr, PROC_TIME_TO_SET)

        # Keep reading MU parameters for MONITOR_ALIVE_SECONDS
        end_time = time.time() + MONITOR_ALIVE_SECONDS
        while time.time() < end_time:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
            mu_width = sim.get_value(f"{config.STATION1_PATH}.MU", "MUWidth")
            mu_height = sim.get_value(f"{config.STATION1_PATH}.MU", "MUHeight")
            print(f"numMU: {num_mu} | MUWidth: {mu_width} | MUHeight: {mu_height}")
            time.sleep(1.0)



if __name__ == "__main__":
    main()

