#!/usr/bin/env python3
"""Simulation Basics - Real connect to Technomatix and read/write ProcTime"""

# Allow running this file directly: add simulation_client to sys.path
import os, sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from configuration.settings import config
from services.simulation_communication.client import TechnomatixConnector

import time

# Simple demo parameters (edit these numbers only)
PROC_TIME_TO_SET = 18.0      # minutes to write to Station1.ProcTime
MONITOR_ALIVE_SECONDS = 60.0 # seconds to keep reading MU parameters
SIMULATION_SPEED = 0.1       # slow down simulation (0.1 = 10% speed)


def safe_read_mu_properties(sim, delay_seconds=0.5):
    """Safely read MU properties after ensuring MU is stable at station."""
    try:
        # Wait for MU to be stable (not in transition)
        time.sleep(delay_seconds)

        # Try to read MU properties with minimal COM calls
        mu_width = sim.ps.GetValue(f"{config.STATION1_PATH}.MU.MUWidth")
        mu_height = sim.ps.GetValue(f"{config.STATION1_PATH}.MU.MUHeight")

        return mu_width, mu_height
    except:
        # If any error occurs, return None values
        return None, None


def main():

    with TechnomatixConnector(model_path=config.MODEL_PATH) as sim:

        station_path = config.STATION1_PATH
        proc_attr = config.STATION1_PROC_TIME_ATTR

        # Slow down simulation to reduce void errors
        sim.ps.SetValue(".Models.Model.SimSpeed", SIMULATION_SPEED)
        print(f"Set simulation speed to: {SIMULATION_SPEED} (slower = more stable)")

        # Read and write ProcTime
        current_proc_time = sim.get_value(station_path, proc_attr)
        print(f"Current ProcTime: {current_proc_time}")

        sim.set_value(station_path, proc_attr, PROC_TIME_TO_SET)
        print(f"Set ProcTime to: {PROC_TIME_TO_SET}")

        # Track MU state to detect when new parts enter
        previous_num_mu = 0
        mu_stable_time = None

        end_time = time.time() + MONITOR_ALIVE_SECONDS
        while time.time() < end_time:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
            current_num_mu = int(num_mu) if num_mu else 0

            # Detect when MU enters station
            if current_num_mu > previous_num_mu:
                print(f"🔵 MU entered station! numMU: {current_num_mu}")
                mu_stable_time = time.time() + 1.0  # Wait 1 second for stability

            # Read MU properties only when MU has been stable
            elif current_num_mu > 0 and mu_stable_time and time.time() >= mu_stable_time:
                mu_width, mu_height = safe_read_mu_properties(sim)
                print(f"📏 MU Properties - Width: {mu_width}, Height: {mu_height}")
                mu_stable_time = None  # Only read once per MU

            # Detect when MU leaves station
            elif current_num_mu < previous_num_mu:
                print(f"🔴 MU left station! numMU: {current_num_mu}")
                mu_stable_time = None

            # Regular status update
            proc_time = sim.get_value(station_path, proc_attr)
            print(f"Status - numMU: {current_num_mu} | ProcTime: {proc_time}")

            previous_num_mu = current_num_mu
            time.sleep(1.0)


if __name__ == "__main__":
    main()

