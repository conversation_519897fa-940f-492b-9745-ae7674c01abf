#!/usr/bin/env python3
"""Simulation Basics - Real connect to Technomatix and read/write ProcTime"""

# Allow running this file directly: add simulation_client to sys.path
import os, sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from configuration.settings import config
from services.simulation_communication.client import TechnomatixConnector

import time

# Simple demo parameters (edit these two numbers only)
PROC_TIME_TO_SET = 18.0      # minutes to write to Station1.ProcTime
MONITOR_ALIVE_SECONDS = 60.0 # seconds to keep reading MU parameters


def _resolve_mu_value(sim: TechnomatixConnector, attr_name: str):
    """Robustly resolve MU attribute across different model versions.
    Tries: first MU from contentsList -> .MU -> .cont (with guard on numMU)
    Returns the value or None.
    """
    # Guard: if no MU present, don't probe paths that may be Void
    try:
        num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
        if not num_mu or int(num_mu) <= 0:
            return None
    except Exception:
        pass

    prefixes = []

    # Try to get first MU from contentsList
    try:
        contents_path = f"{config.STATION1_PATH}.{config.STATION1_CONTENTS_LIST_ATTR}"
        contents = sim.ps.GetValue(contents_path)
        first_mu = None
        if contents:
            if hasattr(contents, 'Count') and getattr(contents, 'Count', 0) > 0:
                try:
                    first_mu = contents.Item(1)
                except Exception:
                    first_mu = None
            elif isinstance(contents, (tuple, list)) and len(contents) > 0:
                first_mu = contents[0]
        if first_mu:
            prefixes.append(str(first_mu))
    except Exception:
        pass

    # Fallbacks: prefer .MU over .cont because cont can be Void intermittently
    prefixes.extend([
        f"{config.STATION1_PATH}.MU",
        f"{config.STATION1_PATH}.cont",
    ])

    # Try each prefix with small retries to ride out handover timing
    for p in prefixes:
        for _ in range(3):
            try:
                val = sim.ps.GetValue(f"{p}.{attr_name}")
                if val is not None:
                    return val
            except Exception:
                pass
            time.sleep(0.1)
    return None


def main():

    with TechnomatixConnector(model_path=config.MODEL_PATH) as sim:

        station_path = config.STATION1_PATH
        proc_attr = config.STATION1_PROC_TIME_ATTR

        # Read and write ProcTime
        sim.get_value(station_path, proc_attr)
        sim.set_value(station_path, proc_attr, PROC_TIME_TO_SET)

        # Keep reading MU parameters for MONITOR_ALIVE_SECONDS
        end_time = time.time() + MONITOR_ALIVE_SECONDS
        while time.time() < end_time:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
            mu_width = _resolve_mu_value(sim, "MUWidth")
            mu_height = _resolve_mu_value(sim, "MUHeight")
            print(f"numMU: {num_mu} | MUWidth: {mu_width} | MUHeight: {mu_height}")
            time.sleep(1.0)



if __name__ == "__main__":
    main()

