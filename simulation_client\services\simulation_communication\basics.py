#!/usr/bin/env python3
"""Simulation Basics - Real connect to Technomatix and read/write ProcTime"""

# Allow running this file directly: add simulation_client to sys.path
import os, sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from configuration.settings import config
from services.simulation_communication.client import TechnomatixConnector

import time

# Simple demo parameters (edit these two numbers only)
PROC_TIME_TO_SET = 18.0      # minutes to write to Station1.ProcTime
MONITOR_ALIVE_SECONDS = 60.0 # seconds to keep reading MU parameters


def _resolve_mu_value(sim: TechnomatixConnector, attr_name: str):
    """Robustly resolve MU attribute across different model versions.
    Tries: first MU from contentsList -> .MU -> .cont (with guard on numMU)
    Returns the value or None.
    """
    # Double-check guard: if no MU present, don't probe paths that may be Void
    try:
        num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
        if not num_mu or int(num_mu) <= 0:
            return None
    except Exception:
        return None

    prefixes = []

    # Try to get first MU from contentsList
    try:
        contents_path = f"{config.STATION1_PATH}.{config.STATION1_CONTENTS_LIST_ATTR}"
        contents = sim.ps.GetValue(contents_path)
        first_mu = None
        if contents:
            if hasattr(contents, 'Count') and getattr(contents, 'Count', 0) > 0:
                try:
                    first_mu = contents.Item(1)
                except Exception:
                    first_mu = None
            elif isinstance(contents, (tuple, list)) and len(contents) > 0:
                first_mu = contents[0]
        if first_mu:
            prefixes.append(str(first_mu))
    except Exception:
        pass

    # Fallbacks: prefer .MU over .cont because cont can be Void intermittently
    prefixes.extend([
        f"{config.STATION1_PATH}.MU",
        f"{config.STATION1_PATH}.cont",
    ])

    # Try each prefix with small retries to ride out handover timing
    for p in prefixes:
        for _ in range(2):  # Reduced retries to fail faster
            try:
                # Re-check numMU before each attempt to avoid void access
                num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
                if not num_mu or int(num_mu) <= 0:
                    return None

                val = sim.ps.GetValue(f"{p}.{attr_name}")
                if val is not None:
                    return val
            except Exception:
                pass
            time.sleep(0.05)  # Shorter sleep
    return None


def _get_mu_attr_stable(sim: TechnomatixConnector, attr_name: str, timeout: float = 0.8, poll_interval: float = 0.05):
    """Read MU attribute robustly during handover timing.

    If numMU <= 0 -> return None immediately.
    Otherwise, retry until timeout for attr to be non-None.
    """
    end = time.time() + timeout
    while time.time() < end:
        try:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
            if not num_mu or int(num_mu) <= 0:
                return None
        except Exception:
            return None
        val = _resolve_mu_value(sim, attr_name)
        if val is not None:
            return val
        time.sleep(poll_interval)
    return None


def _get_mu_dims_stable(sim: TechnomatixConnector, timeout: float = 1.0, poll_interval: float = 0.05):
    """Get MUWidth and MUHeight together with stabilization to avoid mismatched None.
    Returns a tuple (width, height) or (None, None) if no MU.
    """
    end = time.time() + timeout
    while time.time() < end:
        try:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
            if not num_mu or int(num_mu) <= 0:
                return None, None
        except Exception:
            return None, None
        w = _resolve_mu_value(sim, "MUWidth")
        h = _resolve_mu_value(sim, "MUHeight")
        if w is not None and h is not None:
            return w, h
        time.sleep(poll_interval)
    return None, None





def main():

    with TechnomatixConnector(model_path=config.MODEL_PATH) as sim:

        station_path = config.STATION1_PATH
        proc_attr = config.STATION1_PROC_TIME_ATTR

        # Read and write ProcTime
        sim.get_value(station_path, proc_attr)
        sim.set_value(station_path, proc_attr, PROC_TIME_TO_SET)

        # Keep reading MU parameters for MONITOR_ALIVE_SECONDS
        end_time = time.time() + MONITOR_ALIVE_SECONDS
        while time.time() < end_time:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
            # Use stable COM-based method only
            w, h = _get_mu_dims_stable(sim)
            print(f"numMU: {num_mu} | MUWidth: {w} | MUHeight: {h}")
            time.sleep(1.0)



if __name__ == "__main__":
    main()

