#!/usr/bin/env python3
"""Simulation Basics - Real connect to Technomatix and read/write ProcTime"""

# Allow running this file directly: add simulation_client to sys.path
import os, sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from configuration.settings import config
from services.simulation_communication.client import TechnomatixConnector

import time

# Simple demo parameters (edit these two numbers only)
PROC_TIME_TO_SET = 18.0      # minutes to write to Station1.ProcTime
MONITOR_ALIVE_SECONDS = 60.0 # seconds to keep reading MU parameters


def _safe_get_mu_attr(sim: TechnomatixConnector, attr_name: str):
    """Safely get MU attribute with Plant Simulation 15.01 void protection."""
    try:
        # First check if we have any MUs at all
        num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
        if not num_mu or int(num_mu) <= 0:
            return None

        # For PS 15.01, check MU count stability before accessing properties
        # Quick double-check to avoid accessing during transitions
        time.sleep(0.05)  # Give PS time to settle
        num_mu_check = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)
        if not num_mu_check or int(num_mu_check) <= 0 or num_mu != num_mu_check:
            return None  # MU count changed, skip this attempt

        # Try to access MU attribute using direct COM call with error suppression
        try:
            full_path = f"{config.STATION1_PATH}.MU.{attr_name}"
            return sim.ps.GetValue(full_path)
        except Exception:
            # Any COM error (including void errors) - just return None silently
            return None

    except Exception:
        return None


def main():

    with TechnomatixConnector(model_path=config.MODEL_PATH) as sim:

        station_path = config.STATION1_PATH
        proc_attr = config.STATION1_PROC_TIME_ATTR

        # Read and write ProcTime
        sim.get_value(station_path, proc_attr)
        sim.set_value(station_path, proc_attr, PROC_TIME_TO_SET)

        # Keep reading MU parameters for MONITOR_ALIVE_SECONDS
        end_time = time.time() + MONITOR_ALIVE_SECONDS
        while time.time() < end_time:
            num_mu = sim.get_value(config.STATION1_PATH, config.STATION1_NUM_MU_ATTR)

            # Only try to get MU properties if we have MUs
            mu_width = None
            mu_height = None

            if num_mu and int(num_mu) > 0:
                # Extra delay for PS 15.01 stability before accessing MU properties
                time.sleep(0.1)

                # Use safe method for MU attributes (handles PS 15.01 void errors)
                mu_width = _safe_get_mu_attr(sim, "MUWidth")
                mu_height = _safe_get_mu_attr(sim, "MUHeight")

            print(f"numMU: {num_mu} | MUWidth: {mu_width} | MUHeight: {mu_height}")
            time.sleep(1.0)



if __name__ == "__main__":
    main()

