#!/usr/bin/env python3
# Simple script: print mean ProductionTime over all records
import os, sys, math, numpy as np
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)
from data_analytics_application.services.data_analytics.data_fetch import get_record_ids, fetch_production_time

ids = get_record_ids()
vals = []
for rid in ids:
    v = fetch_production_time(rid)
    if v is not None and math.isfinite(v):
        vals.append(v)
if not vals:
    print("No production times found.")
else:
    print(f"Mean ProductionTime: {float(np.mean(vals)):.4f}")

