#!/usr/bin/env python3
"""
Test A: AAS Server Connection
Tests if we can connect to AAS server and pull processing time values
"""

import sys
import os
import requests

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from main.config import Config

def test_aas_connection():
    """Test basic AAS server connection"""
    config = Config()
    base_url = f"http://{config.AAS_HOST}:{config.AAS_PORT}"
    
    try:
        print(f"Testing connection to AAS server: {base_url}")
        response = requests.get(f"{base_url}/shells", timeout=5)
        
        if response.status_code in [200, 404]:  # 404 is normal for base endpoint
            print(" AAS server is reachable")
            return True
        else:
            print(f" AAS server returned status code: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f" Failed to connect to AAS server: {e}")
        return False

def test_production_time_retrieval():
    """Test retrieving production time from AAS server"""
    config = Config()
    
    try:
        print(f"Testing production time endpoint...")
        print(f"URL: {config.AAS_PRODUCTION_TIME_URL}")
        
        response = requests.get(config.AAS_PRODUCTION_TIME_URL, timeout=5)
        
        if response.status_code == 200:
            try:
                # Try JSON parsing first
                data = response.json()
                if isinstance(data, dict) and 'value' in data:
                    time_value = float(data['value'])
                elif isinstance(data, (int, float)):
                    time_value = float(data)
                else:
                    time_value = float(data)
                    
                print(f" Production time from AAS: {time_value}")
                return time_value
                
            except:
                # Try raw text parsing, removing quotes if present
                try:
                    text = response.text.strip().strip('"\'')
                    time_value = float(text)
                    print(f" Production time from AAS (raw): {time_value}")
                    return time_value
                except:
                    print(f" Cannot parse AAS response: {response.text}")
                    return None
        else:
            print(f" AAS endpoint returned status code: {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f" Failed to get production time from AAS: {e}")
        return None

def main():
    print("=== Test A: AAS Server Connection ===")
    print("Testing AAS server connection and data retrieval...")
    
    # Test 1: Basic connection
    print("\n1. Testing AAS server connection...")
    if not test_aas_connection():
        print(" Cannot proceed without AAS connection")
        return 1
    
    # Test 2: Get production time
    print("\n2. Testing production time retrieval...")
    production_time = test_production_time_retrieval()
    
    if production_time is not None:
        print(f" Successfully retrieved production time: {production_time}")
        print("\n Test A completed successfully!")
        return 0
    else:
        print(" Failed to retrieve production time")
        return 1

if __name__ == "__main__":
    sys.exit(main())
