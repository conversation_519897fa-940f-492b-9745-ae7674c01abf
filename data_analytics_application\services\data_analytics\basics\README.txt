This folder contains very small, single-purpose scripts to run each task independently:

- mean.py      -> prints the mean ProductionTime over all records
- dist.py      -> prints the best-fit distribution (requires `fitter`), shows plot if matplotlib is available
- xgb.py       -> trains XGBoost model on Factors and lets you enter factors to get a prediction; saves ERF-XGB_milling.pkl here

Run examples (from repo root):
  python data_analytics_application/services/data_analytics/basics/mean.py
  python data_analytics_application/services/data_analytics/basics/dist.py
  python data_analytics_application/services/data_analytics/basics/xgb.py

