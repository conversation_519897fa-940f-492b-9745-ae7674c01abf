#!/usr/bin/env python3
"""
AAS Utilities
Helper functions and utilities for AAS communication
"""

import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class AASValidator:
    """Validates AAS data structures and operations"""
    
    @staticmethod
    def validate_submodel_structure(submodel_data: Dict[str, Any]) -> bool:
        """Validate submodel structure"""
        required_fields = ['id', 'properties']
        return all(field in submodel_data for field in required_fields)
    
    @staticmethod
    def validate_property_value(property_data: Dict[str, Any]) -> bool:
        """Validate property value structure"""
        return 'value' in property_data


class AASDataProcessor:
    """Processes and transforms AAS data"""
    
    @staticmethod
    def extract_property_values(properties: Dict[str, Dict]) -> Dict[str, Any]:
        """Extract values from AAS property structure"""
        return {key: prop.get('value') for key, prop in properties.items()}
    
    @staticmethod
    def format_property_for_aas(key: str, value: Any, unit: str = "") -> Dict[str, Any]:
        """Format value for AAS property structure"""
        return {
            "value": value,
            "unit": unit
        }


class AASConnectionHelper:
    """Helper functions for AAS connections"""
    
    @staticmethod
    def build_endpoint_url(base_url: str, aas_id: str, submodel_id: str = None) -> str:
        """Build AAS endpoint URL"""
        url = f"{base_url}/aas/{aas_id}"
        if submodel_id:
            url += f"/submodels/{submodel_id}"
        return url
    
    @staticmethod
    def parse_aas_response(response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AAS response data"""
        if 'submodels' in response_data:
            return response_data['submodels']
        return response_data


def get_default_aas_structure() -> Dict[str, Any]:
    """Get default AAS structure template"""
    return {
        "identification": {
            "id": "DefaultAAS",
            "idType": "Custom"
        },
        "submodels": {
            "PartAttributes": {
                "id": "PartAttributesSubmodel",
                "properties": {}
            },
            "ProcessingParameters": {
                "id": "ProcessingParametersSubmodel", 
                "properties": {}
            }
        }
    }


def log_aas_operation(operation: str, aas_id: str, success: bool, details: str = ""):
    """Log AAS operations"""
    level = logging.INFO if success else logging.ERROR
    status = "SUCCESS" if success else "FAILED"
    message = f"AAS {operation} - {status} - AAS ID: {aas_id}"
    if details:
        message += f" - {details}"
    logger.log(level, message)
