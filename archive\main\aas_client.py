#!/usr/bin/env python3
"""
AAS Client Module
Handles communication with Asset Administration Shell (AAS) server
"""

import requests
import logging
from typing import Optional
from .config import Config

class AASClient:
    """Client for communicating with AAS server"""
    
    def __init__(self):
        """Initialize AAS client with configuration"""
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        self.base_url = f"http://{self.config.AAS_HOST}:{self.config.AAS_PORT}"
    
    def test_connection(self) -> bool:
        """
        Test basic connection to AAS server
        
        Returns:
            True if server is reachable, False otherwise
        """
        try:
            self.logger.info(f"Testing connection to AAS server: {self.base_url}")
            response = requests.get(f"{self.base_url}/shells", timeout=self.config.AAS_TIMEOUT)
            
            if response.status_code in [200, 404]:  # 404 is normal for base endpoint
                self.logger.info(" AAS server is reachable")
                return True
            else:
                self.logger.warning(f"AAS server returned status code: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to connect to AAS server: {e}")
            return False
    
    def get_production_time(self) -> Optional[float]:
        """
        Get production time value from AAS server
        
        Returns:
            Production time in seconds, or None if error
        """
        try:
            self.logger.debug(f"Requesting production time from: {self.config.AAS_PRODUCTION_TIME_URL}")
            response = requests.get(self.config.AAS_PRODUCTION_TIME_URL, timeout=self.config.AAS_TIMEOUT)
            
            if response.status_code == 200:
                try:
                    # Try JSON parsing first
                    data = response.json()
                    if isinstance(data, dict) and 'value' in data:
                        time_value = float(data['value'])
                    elif isinstance(data, (int, float)):
                        time_value = float(data)
                    else:
                        time_value = float(data)
                        
                    self.logger.info(f"Production time from AAS: {time_value}")
                    return time_value
                    
                except:
                    # Try raw text parsing, removing quotes if present
                    try:
                        text = response.text.strip().strip('"\'')
                        time_value = float(text)
                        self.logger.info(f"Production time from AAS (raw): {time_value}")
                        return time_value
                    except:
                        self.logger.error(f"Cannot parse AAS response: {response.text}")
                        return None
            else:
                self.logger.error(f"AAS endpoint returned status code: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to get production time from AAS: {e}")
            return None
    
    def get_production_time_with_fallback(self, fallback_value: float = 20.0) -> float:
        """
        Get production time with fallback value if AAS is unavailable
        
        Args:
            fallback_value: Value to return if AAS is unavailable
            
        Returns:
            Production time from AAS or fallback value
        """
        production_time = self.get_production_time()
        
        if production_time is not None:
            return production_time
        else:
            self.logger.warning(f"Using fallback production time: {fallback_value}")
            return fallback_value
